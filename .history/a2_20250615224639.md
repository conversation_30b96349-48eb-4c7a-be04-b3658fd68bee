# 智盒工坊 MVP 最终执行规划 (V2 - 聚焦版) - 详细版

## 1. 核心目标与定位
- **核心目标**
  - **时间框架:** `3-4个月内`
  - **产品形态:** 上线三大系统闭环
    - 客户电商前台
    - 供应商专属后台
    - 内部运营后台
  - **验证方式:** 服务 **10-20个** 付费种子用户
  - **根本目的:**
    - 跑通从在线下单到工厂交付的端到端业务流程
    - **验证商业模式的核心价值** (市场是否愿意为便捷/透明买单)
- **市场定位**
  - **区域聚焦:** **深圳及珠三角**
    - **优势:**
      - 目标客群(新消费品牌/电商)密度高
      - 便于初期进行精准的市场推广与客户拜访
      - 本地化供应链网络，物流与沟通便捷
  - **客群聚焦:** **初创消费品牌**
    - **痛点匹配:**
      - 对品牌形象有要求
      - 存在小批量、多批次包装需求
      - 内部普遍缺乏专业的采购或包装工程师
  - **产品聚焦:** **“双插盒”** 与 **“锁底盒”**
    - **优势:**
      - 工业应用最广泛、最基础的盒型
      - 市场需求量大，目标客户明确
      - 生产工艺高度重合，能最大化复用开发逻辑
      - **有效降低初期报价引擎的复杂度**

## 2. 核心用户与场景
- **核心用户**
  - **客户 (Client)**
    - **画像:** 初创品牌主理人 / 电商运营经理
    - **痛点:**
      - **传统采购流程:** 报价不透明、沟通冗长、起订量(MOQ)高、响应慢
    - **期望:**
      - 获得像在线购物一样**简单、高效、透明**的体验
      - 快速获取高质量包装，专注于核心业务
  - **供应商 (Supplier)**
    - **画像:** 具有前瞻思维、愿意拥抱数字化的本地优质印刷厂
    - **痛点:**
      - 厌倦通过微信/电话反复沟通模糊不清的订单需求
    - **期望:**
      - 接收**规格清晰、文件标准**的“标准件”订单
      - 通过专业后台系统化管理订单，**提升内部生产效率**
- **核心端到端场景 (用户旅程)**
  - **1. 发现与选择:** 客户访问网站，根据产品特性选择 **`双插盒`** 或 **`锁底盒`**
  - **2. 配置与决策:** 输入尺寸/工艺/数量，**实时获得报价与货期**，产生信任感与掌控感
  - **3. 支付与交付:** 在线支付成功，并上传品牌设计稿 (AI/PDF)
  - **4. 内部流转:**
    - 内部后台接收订单 -> 文件规范化处理(添加出血等) -> **手动指派**给最合适的供应商
  - **5. 供应商协同:** 供应商登录后台，**接单**并**下载标准生产文件** (Single Source of Truth)
  - **6. 生产与发货:** 线下完成生产，线上更新状态为“已发货”并录入运单号
  - **7. 闭环与追踪:** 客户收到发货通知，并可在“我的订单”中追踪实时物流

## 3. MVP 产品范围：三大核心系统
- **A. 客户电商平台 (门面)**
  - **产品策略:** **严格限制选项**，是MVP成功的关键
    - `2种盒型`, `2种纸张`, `2种工艺`
  - **自动化引擎:** 提供“魔法感”，本质是**基于规则和价格表的前端快速计算器**
  - **标准功能:** 采用业界成熟方案，确保交易流程稳定可靠
- **B. 供应商专属后台 (契约工具)**
  - **设计思路:** **任务驱动**，让供应商清晰地知道“下一步做什么”
  - **核心任务:**
    - **接收标准指令** (下载标准文件)
    - **反馈关键状态** (接单/发货)
  - **明确范围外 (Postpone):**
    - **财务结算:** 初期线下处理，风险高/开发复杂
    - **在线聊天:** 用户体验不如微信，没必要重复造轮子
- **C. 内部运营后台 (中央神经系统)**
  - **核心价值:**
    - **桥接:** `客户(非标信息) -> 内部(标准化) -> 供应商(标准指令)`
    - **监控:** 掌握所有订单的流转状态
  - **MVP精髓:** **“手动订单派发”**，以经验丰富的人脑判断替代初期复杂的自动化派单算法

## 4. 关键“自动化”引擎解构
- **自动化报价引擎**
  - **核心:** 参数化的计价公式
  - **变量处理:** 根据 **`盒型`** 参数进行动态调整
    - **纸张费:** `锁底盒`的展开结构更复杂，调用一个比`双插盒`**稍大的纸张消耗系数**
    - **后道工艺费:** `锁底盒`可叠加一个微小的**“结构复杂度”附加费**
- **自动化货期引擎**
  - **核心:** 基于查表的规则 (`IF-THEN`)
  - **变量处理:** 生产时间查询表增加 **`盒型`** 维度
    - **示例:** 同等条件下，`锁底盒`的粘合工序更复杂，生产时间 **+0.5 ~ 1 工作日**

## 5. MVP 实施路线图与时间线
- **总目标:** **2025年10月底前上线**
- **第一阶段 (4周): 准备与设计**
  - **目的:** 蓝图绘制，"Measure twice, cut once" (三思而后行)
- **第二阶段 (6周): 并行开发**
  - **目的:** 高效执行，多团队同步开工，快速构建产品主体
- **第三阶段 (3周): 集成、测试与内容填充**
  - **目的:** “从零件到整车”的组装、调试和数据录入
- **第四阶段 (10月起): 上线与运营**
  - **目的:** “是骡子是马，拉出来遛遛”，在真实场景中收集反馈

## 6. 成功标准与关键指标
- **技术标准:**
  - **要求:** 平台稳定运行，核心报价计算无严重错误
- **业务标准:**
  - **里程碑:** **成功完成 ≥10个 付费订单**
  - **意义:** 完成商业闭环的最终验证，证明市场愿意付费
- **用户标准:**
  - **核心:** **客户与供应商愿意复购/复用**
  - **意义:** 衡量产品核心价值与用户粘性
- **关键指标:**
  - **`订单转化率`:** 衡量价格与体验的吸引力
  - **`NPS (净推荐值)`:** 衡量口碑与客户满意度
  - **`订单平均交付周期`:** 衡量运营效率与承诺兑现能力
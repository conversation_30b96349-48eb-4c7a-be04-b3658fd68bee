# 智盒工坊 MVP 最终执行规划 (V2 - 聚焦版)

## 1. 核心目标与定位
- **核心目标**
  - **时间:** 3-4个月内上线
  - **产品:** 三大系统闭环 (客户/供应商/内部)
  - **验证:**
    - 服务 **10-20个** 种子用户
    - 完整跑通商业模式
- **市场定位**
  - **区域:** 深圳及珠三角
  - **客群:** 初创消费品牌
  - **产品:** 提供 **“双插盒”** 与 **“锁底盒”** 在线定制服务

## 2. 核心用户与场景
- **核心用户**
  - **客户 (Client):** 缺专业知识，有小批量/快反应需求
  - **供应商 (Supplier):** 愿拥抱数字化的本地优质印刷厂
- **核心端到端场景**
  - 1. 客户访问 -> **选择盒型 (双插盒/锁底盒)**
  - 2. 在线配置 -> **立即获得报价/货期**
  - 3. 支付 -> 上传设计稿
  - 4. 内部处理 -> **指派订单**
  - 5. 供应商登录 -> **接单/下载生产文件**
  - 6. 线下生产 -> **后台更新发货信息**
  - 7. 客户追踪物流

## 3. MVP 产品范围：三大核心系统
- **A. 客户电商平台**
  - **产品品类:** `纸质包装` -> `卡盒`
  - **盒型选择器:** **“双插盒”** vs **“锁底盒”** (二选一)
  - **在线配置器**
    - 尺寸 (L,W,H)
    - **2种** 预设纸张
    - **2种** 预设工艺
    - 数量
  - **自动化引擎**
    - 即时报价
    - 即时货期
  - **标准电商功能**
    - 购物车/支付
    - 订单历史/追踪
- **B. 供应商专属后台**
  - **核心功能**
    - 安全登录
    - 工作台 (核心任务提醒/接单)
    - 订单管理 (列表/详情)
    - **下载标准生产文件**
    - **更新发货状态**
  - **范围外 (Postpone)**
    - 财务结算
    - 在线聊天
    - 数据分析
- **C. 内部运营后台**
  - **核心功能**
    - 订单总览
    - 文件处理流程 (上传/下载)
    - **手动订单派发 (核心)**
    - 状态监控

## 4. 关键“自动化”引擎解构
- **自动化报价引擎**
  - **纸张费计算:** 需区分盒型 (`锁底盒`更耗材)
  - **后道工艺费:** `锁底盒`可设“结构复杂度”附加费
- **自动化货期引擎**
  - **生产天数:** 查询表增加“盒型”维度 (`锁底盒` +0.5~1天)

## 5. MVP 实施路线图与时间线
- **总目标:** **2025年10月底前上线**
- **第一阶段 (4周): 准备与设计**
  - PRD, UI/UX, DB设计, 供应商沟通
- **第二阶段 (6周): 并行开发**
  - 前端/后端/各后台同步进行
- **第三阶段 (3周): 集成测试与填充**
  - 联调/修BUG
  - 录入真实价格
  - 种子供应商内测
- **第四阶段 (启动): 上线与运营**
  - 正式上线
  - 精准邀请种子客户

## 6. 成功标准与关键指标
- **技术标准:** 平台稳定, 计算准确
- **业务标准:** 成功交付 **≥10个** 付费订单
- **用户标准:** 客户/供应商满意, 愿复用
- **关键指标**
  - 订单转化率
  - 客户满意度 (NPS)
  - 订单平均交付周期